"""
Reporting system for the trading bot
"""
import json
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Optional
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from loguru import logger

from models import Portfolio, Position, Order, TradingSignal
from config import config


class ReportGenerator:
    """Generate trading reports and analytics"""
    
    def __init__(self):
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
        
    def generate_daily_report(
        self, 
        portfolio: Portfolio, 
        signals: List[TradingSignal],
        risk_summary: Dict[str, any]
    ) -> Dict[str, any]:
        """Generate daily trading report"""
        
        today = datetime.now().date()
        
        # Calculate daily statistics
        daily_trades = [order for order in portfolio.orders 
                       if order.created_at.date() == today]
        
        daily_pnl = sum(pos.pnl for pos in portfolio.positions 
                       if pos.closed_at and pos.closed_at.date() == today)
        
        open_positions = portfolio.open_positions
        
        # Calculate win rate
        closed_positions_today = [pos for pos in portfolio.positions 
                                 if pos.closed_at and pos.closed_at.date() == today]
        
        winning_trades = len([pos for pos in closed_positions_today if pos.pnl > 0])
        total_trades = len(closed_positions_today)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Generate signals summary
        daily_signals = [signal for signal in signals 
                        if signal.timestamp.date() == today]
        
        entry_signals = len([s for s in daily_signals if s.signal_type == "SHORT_ENTRY"])
        exit_signals = len([s for s in daily_signals if s.signal_type == "SHORT_EXIT"])
        
        report = {
            "date": today.isoformat(),
            "portfolio": {
                "total_value": portfolio.total_value,
                "cash": portfolio.cash,
                "daily_pnl": daily_pnl,
                "total_pnl": portfolio.total_pnl,
                "open_positions": len(open_positions),
                "positions_value": sum(pos.market_value for pos in open_positions)
            },
            "trading": {
                "daily_trades": len(daily_trades),
                "win_rate": win_rate,
                "winning_trades": winning_trades,
                "total_trades": total_trades,
                "entry_signals": entry_signals,
                "exit_signals": exit_signals
            },
            "risk": risk_summary,
            "positions": [
                {
                    "symbol": pos.symbol,
                    "side": pos.side,
                    "quantity": pos.quantity,
                    "entry_price": pos.entry_price,
                    "current_price": pos.current_price,
                    "unrealized_pnl": pos.unrealized_pnl,
                    "unrealized_pnl_percent": pos.unrealized_pnl_percent
                }
                for pos in open_positions
            ]
        }
        
        return report
    
    def save_daily_report(self, report: Dict[str, any]):
        """Save daily report to file"""
        date_str = report["date"]
        filename = self.reports_dir / f"daily_report_{date_str}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Daily report saved to {filename}")
    
    def generate_performance_chart(self, portfolio_history: List[Dict]) -> str:
        """Generate performance chart"""
        if not portfolio_history:
            return ""
        
        df = pd.DataFrame(portfolio_history)
        df['date'] = pd.to_datetime(df['date'])
        
        plt.figure(figsize=(12, 8))
        
        # Portfolio value over time
        plt.subplot(2, 2, 1)
        plt.plot(df['date'], df['total_value'], label='Portfolio Value', color='blue')
        plt.axhline(y=config.capital, color='red', linestyle='--', label='Initial Capital')
        plt.title('Portfolio Value Over Time')
        plt.xlabel('Date')
        plt.ylabel('Value ($)')
        plt.legend()
        plt.xticks(rotation=45)
        
        # Daily P&L
        plt.subplot(2, 2, 2)
        plt.bar(df['date'], df['daily_pnl'], color=['green' if x >= 0 else 'red' for x in df['daily_pnl']])
        plt.title('Daily P&L')
        plt.xlabel('Date')
        plt.ylabel('P&L ($)')
        plt.xticks(rotation=45)
        
        # Cumulative P&L
        plt.subplot(2, 2, 3)
        df['cumulative_pnl'] = df['daily_pnl'].cumsum()
        plt.plot(df['date'], df['cumulative_pnl'], color='purple')
        plt.title('Cumulative P&L')
        plt.xlabel('Date')
        plt.ylabel('Cumulative P&L ($)')
        plt.xticks(rotation=45)
        
        # Win Rate
        plt.subplot(2, 2, 4)
        plt.plot(df['date'], df['win_rate'], color='orange', marker='o')
        plt.title('Win Rate Over Time')
        plt.xlabel('Date')
        plt.ylabel('Win Rate (%)')
        plt.ylim(0, 100)
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        chart_path = self.reports_dir / f"performance_chart_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Performance chart saved to {chart_path}")
        return str(chart_path)
    
    def generate_html_report(self, report: Dict[str, any]) -> str:
        """Generate HTML report"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading Bot Daily Report - {date}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
                .neutral {{ color: blue; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Trading Bot Daily Report</h1>
                <h2>Date: {date}</h2>
            </div>
            
            <div class="section">
                <h3>Portfolio Summary</h3>
                <p><strong>Total Value:</strong> ${total_value:.2f}</p>
                <p><strong>Cash:</strong> ${cash:.2f}</p>
                <p><strong>Daily P&L:</strong> <span class="{daily_pnl_class}">${daily_pnl:.2f}</span></p>
                <p><strong>Total P&L:</strong> <span class="{total_pnl_class}">${total_pnl:.2f}</span></p>
                <p><strong>Open Positions:</strong> {open_positions}</p>
            </div>
            
            <div class="section">
                <h3>Trading Activity</h3>
                <p><strong>Daily Trades:</strong> {daily_trades}</p>
                <p><strong>Win Rate:</strong> {win_rate:.1f}%</p>
                <p><strong>Entry Signals:</strong> {entry_signals}</p>
                <p><strong>Exit Signals:</strong> {exit_signals}</p>
            </div>
            
            <div class="section">
                <h3>Risk Management</h3>
                <p><strong>Daily P&L:</strong> ${risk_daily_pnl:.2f}</p>
                <p><strong>Remaining Daily Loss:</strong> ${remaining_daily_loss:.2f}</p>
                <p><strong>Exposure:</strong> {exposure_percent:.1f}%</p>
                <p><strong>Max Drawdown:</strong> {max_drawdown:.2f}%</p>
            </div>
            
            <div class="section">
                <h3>Open Positions</h3>
                {positions_table}
            </div>
        </body>
        </html>
        """
        
        # Generate positions table
        positions_html = "<table><tr><th>Symbol</th><th>Side</th><th>Quantity</th><th>Entry Price</th><th>Current Price</th><th>Unrealized P&L</th><th>P&L %</th></tr>"
        
        for pos in report["positions"]:
            pnl_class = "positive" if pos["unrealized_pnl"] >= 0 else "negative"
            positions_html += f"""
            <tr>
                <td>{pos["symbol"]}</td>
                <td>{pos["side"]}</td>
                <td>{pos["quantity"]}</td>
                <td>${pos["entry_price"]:.2f}</td>
                <td>${pos["current_price"]:.2f}</td>
                <td class="{pnl_class}">${pos["unrealized_pnl"]:.2f}</td>
                <td class="{pnl_class}">{pos["unrealized_pnl_percent"]:.2f}%</td>
            </tr>
            """
        
        positions_html += "</table>"
        
        if not report["positions"]:
            positions_html = "<p>No open positions</p>"
        
        # Format the HTML
        html_content = html_template.format(
            date=report["date"],
            total_value=report["portfolio"]["total_value"],
            cash=report["portfolio"]["cash"],
            daily_pnl=report["portfolio"]["daily_pnl"],
            daily_pnl_class="positive" if report["portfolio"]["daily_pnl"] >= 0 else "negative",
            total_pnl=report["portfolio"]["total_pnl"],
            total_pnl_class="positive" if report["portfolio"]["total_pnl"] >= 0 else "negative",
            open_positions=report["portfolio"]["open_positions"],
            daily_trades=report["trading"]["daily_trades"],
            win_rate=report["trading"]["win_rate"],
            entry_signals=report["trading"]["entry_signals"],
            exit_signals=report["trading"]["exit_signals"],
            risk_daily_pnl=report["risk"]["daily_pnl"],
            remaining_daily_loss=report["risk"]["remaining_daily_loss"],
            exposure_percent=report["risk"]["exposure_percent"],
            max_drawdown=report["risk"]["max_drawdown"],
            positions_table=positions_html
        )
        
        # Save HTML report
        html_path = self.reports_dir / f"daily_report_{report['date']}.html"
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        logger.info(f"HTML report saved to {html_path}")
        return str(html_path)
    
    def send_email_report(self, report: Dict[str, any], html_path: str = None):
        """Send email report"""
        if not all([config.report_email, config.smtp_server, config.smtp_username, config.smtp_password]):
            logger.warning("Email configuration incomplete, skipping email report")
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = config.smtp_username
            msg['To'] = config.report_email
            msg['Subject'] = f"Trading Bot Daily Report - {report['date']}"
            
            # Create email body
            body = f"""
            Trading Bot Daily Report - {report['date']}
            
            Portfolio Summary:
            - Total Value: ${report['portfolio']['total_value']:.2f}
            - Daily P&L: ${report['portfolio']['daily_pnl']:.2f}
            - Open Positions: {report['portfolio']['open_positions']}
            
            Trading Activity:
            - Daily Trades: {report['trading']['daily_trades']}
            - Win Rate: {report['trading']['win_rate']:.1f}%
            
            Risk Management:
            - Exposure: {report['risk']['exposure_percent']:.1f}%
            - Max Drawdown: {report['risk']['max_drawdown']:.2f}%
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            # Attach HTML report if available
            if html_path and Path(html_path).exists():
                with open(html_path, 'r') as f:
                    html_content = f.read()
                msg.attach(MimeText(html_content, 'html'))
            
            # Send email
            server = smtplib.SMTP(config.smtp_server, config.smtp_port)
            server.starttls()
            server.login(config.smtp_username, config.smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email report sent to {config.report_email}")
            
        except Exception as e:
            logger.error(f"Failed to send email report: {e}")
