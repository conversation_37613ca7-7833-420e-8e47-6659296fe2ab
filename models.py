"""
Data models for the trading bot
"""
from datetime import datetime
from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field
from decimal import Decimal


class OrderSide(str, Enum):
    """Order side enumeration"""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(str, Enum):
    """Order type enumeration"""
    MARKET = "MKT"
    LIMIT = "LMT"
    STOP = "STP"
    STOP_LIMIT = "STP LMT"


class OrderStatus(str, Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class PositionStatus(str, Enum):
    """Position status enumeration"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"


class Candle(BaseModel):
    """Candlestick data model"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    
    @property
    def is_green(self) -> bool:
        """Check if candle is green (close > open)"""
        return self.close > self.open
    
    @property
    def is_red(self) -> bool:
        """Check if candle is red (close < open)"""
        return self.close < self.open
    
    @property
    def body_size(self) -> float:
        """Get the size of the candle body"""
        return abs(self.close - self.open)
    
    @property
    def price_change_percent(self) -> float:
        """Get price change percentage"""
        if self.open == 0:
            return 0
        return ((self.close - self.open) / self.open) * 100


class Order(BaseModel):
    """Order data model"""
    id: Optional[int] = None
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    commission: float = 0.0


class Position(BaseModel):
    """Position data model"""
    id: str
    symbol: str
    side: OrderSide
    quantity: int
    entry_price: float
    current_price: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    status: PositionStatus = PositionStatus.OPEN
    opened_at: datetime = Field(default_factory=datetime.now)
    closed_at: Optional[datetime] = None
    pnl: float = 0.0
    commission: float = 0.0
    
    @property
    def market_value(self) -> float:
        """Calculate current market value"""
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        """Calculate unrealized P&L"""
        if self.side == OrderSide.SELL:  # Short position
            return (self.entry_price - self.current_price) * self.quantity
        else:  # Long position
            return (self.current_price - self.entry_price) * self.quantity
    
    @property
    def unrealized_pnl_percent(self) -> float:
        """Calculate unrealized P&L percentage"""
        if self.entry_price == 0:
            return 0
        return (self.unrealized_pnl / (self.entry_price * self.quantity)) * 100


class Portfolio(BaseModel):
    """Portfolio data model"""
    cash: float = 0.0
    positions: List[Position] = Field(default_factory=list)
    orders: List[Order] = Field(default_factory=list)
    daily_pnl: float = 0.0
    total_pnl: float = 0.0
    
    @property
    def total_value(self) -> float:
        """Calculate total portfolio value"""
        positions_value = sum(pos.market_value for pos in self.positions if pos.status == PositionStatus.OPEN)
        return self.cash + positions_value
    
    @property
    def open_positions(self) -> List[Position]:
        """Get all open positions"""
        return [pos for pos in self.positions if pos.status == PositionStatus.OPEN]
    
    @property
    def short_positions(self) -> List[Position]:
        """Get all short positions"""
        return [pos for pos in self.open_positions if pos.side == OrderSide.SELL]
    
    def add_position(self, position: Position):
        """Add a new position"""
        self.positions.append(position)
    
    def close_position(self, position_id: str, close_price: float):
        """Close a position"""
        for pos in self.positions:
            if pos.id == position_id and pos.status == PositionStatus.OPEN:
                pos.status = PositionStatus.CLOSED
                pos.closed_at = datetime.now()
                pos.current_price = close_price
                pos.pnl = pos.unrealized_pnl
                break


class TradingSignal(BaseModel):
    """Trading signal data model"""
    symbol: str
    signal_type: str  # "SHORT_ENTRY", "SHORT_EXIT"
    timestamp: datetime = Field(default_factory=datetime.now)
    price: float
    confidence: float = 1.0
    reason: str = ""
