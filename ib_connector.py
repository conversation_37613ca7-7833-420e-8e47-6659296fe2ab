"""
Interactive Brokers API connector
"""
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Callable
from loguru import logger
import pandas as pd

from ib_insync import IB, Stock, Contract, MarketOrder, LimitOrder, util
from ib_insync.objects import BarData

from models import Candle, Order, Position, OrderSide, OrderType, OrderStatus
from config import config


class IBConnector:
    """Interactive Brokers API connector"""
    
    def __init__(self):
        self.ib = IB()
        self.connected = False
        self.contracts: Dict[str, Contract] = {}
        self.price_callbacks: Dict[str, List[Callable]] = {}
        
    async def connect(self) -> bool:
        """Connect to Interactive Brokers"""
        try:
            await self.ib.connectAsync(
                host=config.ib_host,
                port=config.ib_port,
                clientId=config.ib_client_id
            )
            self.connected = True
            logger.info(f"Connected to IB at {config.ib_host}:{config.ib_port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to IB: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Interactive Brokers"""
        if self.connected:
            self.ib.disconnect()
            self.connected = False
            logger.info("Disconnected from IB")
    
    def create_stock_contract(self, symbol: str, exchange: str = "SMART", currency: str = "USD") -> Contract:
        """Create a stock contract"""
        contract = Stock(symbol, exchange, currency)
        self.contracts[symbol] = contract
        return contract
    
    async def get_contract_details(self, symbol: str) -> Optional[Contract]:
        """Get contract details for a symbol"""
        if symbol in self.contracts:
            return self.contracts[symbol]
        
        try:
            contract = self.create_stock_contract(symbol)
            details = await self.ib.reqContractDetailsAsync(contract)
            if details:
                self.contracts[symbol] = details[0].contract
                return details[0].contract
        except Exception as e:
            logger.error(f"Failed to get contract details for {symbol}: {e}")
        
        return None
    
    async def get_historical_data(
        self, 
        symbol: str, 
        duration: str = "1 D", 
        bar_size: str = "1 min",
        what_to_show: str = "TRADES"
    ) -> List[Candle]:
        """Get historical candlestick data"""
        try:
            contract = await self.get_contract_details(symbol)
            if not contract:
                return []
            
            bars = await self.ib.reqHistoricalDataAsync(
                contract,
                endDateTime='',
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=False,
                formatDate=1
            )
            
            candles = []
            for bar in bars:
                candle = Candle(
                    symbol=symbol,
                    timestamp=bar.date,
                    open=float(bar.open),
                    high=float(bar.high),
                    low=float(bar.low),
                    close=float(bar.close),
                    volume=int(bar.volume)
                )
                candles.append(candle)
            
            logger.info(f"Retrieved {len(candles)} candles for {symbol}")
            return candles
            
        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {e}")
            return []
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for a symbol"""
        try:
            contract = await self.get_contract_details(symbol)
            if not contract:
                return None
            
            ticker = self.ib.reqMktData(contract, '', False, False)
            await asyncio.sleep(1)  # Wait for price update
            
            if ticker.marketPrice():
                price = float(ticker.marketPrice())
                self.ib.cancelMktData(contract)
                return price
            elif ticker.close:
                price = float(ticker.close)
                self.ib.cancelMktData(contract)
                return price
                
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
        
        return None
    
    async def place_market_order(
        self, 
        symbol: str, 
        side: OrderSide, 
        quantity: int
    ) -> Optional[Order]:
        """Place a market order"""
        try:
            contract = await self.get_contract_details(symbol)
            if not contract:
                return None
            
            action = "SELL" if side == OrderSide.SELL else "BUY"
            ib_order = MarketOrder(action, quantity)
            
            trade = self.ib.placeOrder(contract, ib_order)
            
            order = Order(
                id=trade.order.orderId,
                symbol=symbol,
                side=side,
                order_type=OrderType.MARKET,
                quantity=quantity,
                status=OrderStatus.SUBMITTED
            )
            
            logger.info(f"Placed market order: {side} {quantity} {symbol}")
            return order
            
        except Exception as e:
            logger.error(f"Failed to place market order: {e}")
            return None
    
    async def place_limit_order(
        self, 
        symbol: str, 
        side: OrderSide, 
        quantity: int, 
        price: float
    ) -> Optional[Order]:
        """Place a limit order"""
        try:
            contract = await self.get_contract_details(symbol)
            if not contract:
                return None
            
            action = "SELL" if side == OrderSide.SELL else "BUY"
            ib_order = LimitOrder(action, quantity, price)
            
            trade = self.ib.placeOrder(contract, ib_order)
            
            order = Order(
                id=trade.order.orderId,
                symbol=symbol,
                side=side,
                order_type=OrderType.LIMIT,
                quantity=quantity,
                price=price,
                status=OrderStatus.SUBMITTED
            )
            
            logger.info(f"Placed limit order: {side} {quantity} {symbol} @ {price}")
            return order
            
        except Exception as e:
            logger.error(f"Failed to place limit order: {e}")
            return None
    
    async def cancel_order(self, order_id: int) -> bool:
        """Cancel an order"""
        try:
            for trade in self.ib.trades():
                if trade.order.orderId == order_id:
                    self.ib.cancelOrder(trade.order)
                    logger.info(f"Cancelled order {order_id}")
                    return True
            return False
        except Exception as e:
            logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_positions(self) -> List[Position]:
        """Get current positions"""
        positions = []
        try:
            ib_positions = self.ib.positions()
            for pos in ib_positions:
                if pos.position != 0:
                    side = OrderSide.SELL if pos.position < 0 else OrderSide.BUY
                    position = Position(
                        id=f"{pos.contract.symbol}_{pos.account}",
                        symbol=pos.contract.symbol,
                        side=side,
                        quantity=abs(int(pos.position)),
                        entry_price=float(pos.avgCost),
                        current_price=0.0  # Will be updated separately
                    )
                    positions.append(position)
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
        
        return positions
    
    async def get_account_summary(self) -> Dict[str, float]:
        """Get account summary"""
        try:
            summary = self.ib.accountSummary()
            result = {}
            for item in summary:
                if item.tag in ['TotalCashValue', 'NetLiquidation', 'UnrealizedPnL', 'RealizedPnL']:
                    result[item.tag] = float(item.value)
            return result
        except Exception as e:
            logger.error(f"Failed to get account summary: {e}")
            return {}
    
    def start_real_time_data(self, symbol: str, callback: Callable):
        """Start real-time data stream for a symbol"""
        if symbol not in self.price_callbacks:
            self.price_callbacks[symbol] = []
        self.price_callbacks[symbol].append(callback)
    
    def stop_real_time_data(self, symbol: str):
        """Stop real-time data stream for a symbol"""
        if symbol in self.price_callbacks:
            del self.price_callbacks[symbol]
