"""
Backtesting module for the trading bot
"""
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Tuple, Optional
import pandas as pd
from loguru import logger

from models import Candle, Position, Portfolio, OrderSide, PositionStatus, TradingSignal
from strategy import ShortStrategy
from risk_manager import RiskManager
from config import config


class BacktestEngine:
    """Backtesting engine for trading strategies"""
    
    def __init__(self):
        self.strategy = ShortStrategy()
        self.risk_manager = RiskManager()
        self.portfolio = Portfolio(cash=config.capital)
        self.trades_history = []
        self.equity_curve = []
        
    def load_historical_data(self, symbol: str, data: List[Dict]) -> List[Candle]:
        """Load historical data and convert to Candle objects"""
        candles = []
        for bar in data:
            candle = Candle(
                symbol=symbol,
                timestamp=datetime.fromisoformat(bar['timestamp']) if isinstance(bar['timestamp'], str) else bar['timestamp'],
                open=float(bar['open']),
                high=float(bar['high']),
                low=float(bar['low']),
                close=float(bar['close']),
                volume=int(bar.get('volume', 0))
            )
            candles.append(candle)
        
        return sorted(candles, key=lambda x: x.timestamp)
    
    def simulate_trade_execution(self, signal: TradingSignal, current_candle: Candle) -> Optional[Position]:
        """Simulate trade execution"""
        if signal.signal_type == "SHORT_ENTRY":
            # Check if we can open new position
            position_size = self.strategy.calculate_position_size(signal.symbol, signal.price)
            position_value = position_size * signal.price
            
            can_trade, reason = self.risk_manager.can_open_new_position(self.portfolio, position_value)
            if not can_trade:
                logger.warning(f"Cannot open position: {reason}")
                return None
            
            # Create short position
            position = Position(
                id=f"{signal.symbol}_{current_candle.timestamp.isoformat()}",
                symbol=signal.symbol,
                side=OrderSide.SELL,
                quantity=position_size,
                entry_price=signal.price,
                current_price=signal.price,
                opened_at=current_candle.timestamp
            )
            
            # Set stop loss and take profit
            position.stop_loss = self.risk_manager.calculate_stop_loss_price(position)
            position.take_profit = self.risk_manager.calculate_take_profit_price(position)
            
            # Update portfolio
            self.portfolio.cash -= position_value  # Reserve cash for short position
            self.portfolio.add_position(position)
            self.strategy.add_position(position)
            
            logger.info(f"Opened SHORT position: {signal.symbol} @ {signal.price}, size: {position_size}")
            return position
            
        elif signal.signal_type == "SHORT_EXIT":
            # Find and close position
            for position in self.portfolio.open_positions:
                if position.symbol == signal.symbol and position.side == OrderSide.SELL:
                    self.close_position(position, signal.price, current_candle.timestamp, "STRATEGY_EXIT")
                    return position
        
        return None
    
    def close_position(self, position: Position, close_price: float, close_time: datetime, reason: str):
        """Close a position"""
        position.current_price = close_price
        position.closed_at = close_time
        position.status = PositionStatus.CLOSED
        
        # Calculate P&L for short position
        pnl = (position.entry_price - close_price) * position.quantity
        position.pnl = pnl
        
        # Update portfolio
        position_value = position.quantity * position.entry_price
        self.portfolio.cash += position_value + pnl  # Return cash + profit/loss
        self.portfolio.total_pnl += pnl
        
        # Update risk manager
        self.risk_manager.update_daily_pnl(pnl)
        
        # Remove from strategy tracking
        self.strategy.remove_position(position.symbol)
        
        # Record trade
        trade_record = {
            "symbol": position.symbol,
            "side": position.side,
            "entry_time": position.opened_at,
            "exit_time": close_time,
            "entry_price": position.entry_price,
            "exit_price": close_price,
            "quantity": position.quantity,
            "pnl": pnl,
            "pnl_percent": (pnl / (position.entry_price * position.quantity)) * 100,
            "duration": (close_time - position.opened_at).total_seconds() / 60,  # minutes
            "exit_reason": reason
        }
        
        self.trades_history.append(trade_record)
        
        logger.info(f"Closed position: {position.symbol} @ {close_price}, P&L: ${pnl:.2f} ({reason})")
    
    def check_risk_management(self, current_candle: Candle):
        """Check risk management for all open positions"""
        positions_to_close = []
        
        for position in self.portfolio.open_positions:
            if position.symbol == current_candle.symbol:
                position.current_price = current_candle.close
                
                risk_action = self.risk_manager.check_position_risk(position, current_candle.close)
                
                if risk_action == "STOP_LOSS":
                    positions_to_close.append((position, position.stop_loss, "STOP_LOSS"))
                elif risk_action == "TAKE_PROFIT":
                    positions_to_close.append((position, position.take_profit, "TAKE_PROFIT"))
        
        # Close positions that hit risk management rules
        for position, close_price, reason in positions_to_close:
            self.close_position(position, close_price, current_candle.timestamp, reason)
    
    def record_equity_point(self, timestamp: datetime):
        """Record equity curve point"""
        total_value = self.portfolio.cash
        
        # Add unrealized P&L from open positions
        for position in self.portfolio.open_positions:
            if position.side == OrderSide.SELL:  # Short position
                unrealized_pnl = (position.entry_price - position.current_price) * position.quantity
            else:  # Long position
                unrealized_pnl = (position.current_price - position.entry_price) * position.quantity
            
            total_value += unrealized_pnl
        
        self.equity_curve.append({
            "timestamp": timestamp,
            "equity": total_value,
            "cash": self.portfolio.cash,
            "open_positions": len(self.portfolio.open_positions),
            "total_pnl": self.portfolio.total_pnl
        })
    
    def run_backtest(self, historical_data: Dict[str, List[Candle]], start_date: datetime = None, end_date: datetime = None) -> Dict:
        """Run backtest on historical data"""
        logger.info("Starting backtest...")
        
        # Combine all candles and sort by timestamp
        all_candles = []
        for symbol, candles in historical_data.items():
            all_candles.extend(candles)
        
        all_candles.sort(key=lambda x: x.timestamp)
        
        # Filter by date range if specified
        if start_date:
            all_candles = [c for c in all_candles if c.timestamp >= start_date]
        if end_date:
            all_candles = [c for c in all_candles if c.timestamp <= end_date]
        
        logger.info(f"Processing {len(all_candles)} candles from {all_candles[0].timestamp} to {all_candles[-1].timestamp}")
        
        # Process each candle
        for i, candle in enumerate(all_candles):
            # Add candle to strategy
            self.strategy.add_candle(candle)
            
            # Check risk management for existing positions
            self.check_risk_management(candle)
            
            # Generate trading signals
            signals = self.strategy.analyze_symbol(candle.symbol, candle.close)
            
            # Execute signals
            for signal in signals:
                self.simulate_trade_execution(signal, candle)
            
            # Record equity curve (every 100 candles to reduce data)
            if i % 100 == 0:
                self.record_equity_point(candle.timestamp)
        
        # Final equity point
        if all_candles:
            self.record_equity_point(all_candles[-1].timestamp)
        
        # Close any remaining open positions at the end
        for position in self.portfolio.open_positions.copy():
            last_candle = next((c for c in reversed(all_candles) if c.symbol == position.symbol), None)
            if last_candle:
                self.close_position(position, last_candle.close, last_candle.timestamp, "BACKTEST_END")
        
        logger.info("Backtest completed")
        return self.generate_backtest_results()
    
    def generate_backtest_results(self) -> Dict:
        """Generate backtest results and statistics"""
        if not self.trades_history:
            return {"error": "No trades executed during backtest"}
        
        trades_df = pd.DataFrame(self.trades_history)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # Calculate statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_pnl = trades_df['pnl'].sum()
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
        
        # Calculate maximum drawdown
        equity_df['peak'] = equity_df['equity'].cummax()
        equity_df['drawdown'] = (equity_df['peak'] - equity_df['equity']) / equity_df['peak'] * 100
        max_drawdown = equity_df['drawdown'].max()
        
        # Calculate returns
        initial_capital = config.capital
        final_equity = equity_df['equity'].iloc[-1] if not equity_df.empty else initial_capital
        total_return = ((final_equity - initial_capital) / initial_capital) * 100
        
        # Calculate Sharpe ratio (simplified)
        daily_returns = trades_df.groupby(trades_df['entry_time'].dt.date)['pnl'].sum()
        sharpe_ratio = (daily_returns.mean() / daily_returns.std()) if daily_returns.std() > 0 else 0
        
        results = {
            "summary": {
                "initial_capital": initial_capital,
                "final_equity": final_equity,
                "total_return": total_return,
                "total_pnl": total_pnl,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio
            },
            "trades": {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "profit_factor": profit_factor,
                "avg_trade_duration": trades_df['duration'].mean()
            },
            "equity_curve": equity_df.to_dict('records'),
            "trades_history": self.trades_history
        }
        
        return results
