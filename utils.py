"""
Utility functions for the trading bot
"""
import json
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pathlib import Path
import pandas as pd
from loguru import logger

from models import Candle


def load_historical_data_from_csv(file_path: str, symbol: str) -> List[Candle]:
    """Load historical data from CSV file"""
    candles = []
    
    try:
        df = pd.read_csv(file_path)
        
        # Expected columns: timestamp, open, high, low, close, volume
        required_columns = ['timestamp', 'open', 'high', 'low', 'close']
        
        if not all(col in df.columns for col in required_columns):
            logger.error(f"CSV file missing required columns: {required_columns}")
            return []
        
        for _, row in df.iterrows():
            candle = Candle(
                symbol=symbol,
                timestamp=pd.to_datetime(row['timestamp']),
                open=float(row['open']),
                high=float(row['high']),
                low=float(row['low']),
                close=float(row['close']),
                volume=int(row.get('volume', 0))
            )
            candles.append(candle)
        
        logger.info(f"Loaded {len(candles)} candles from {file_path}")
        
    except Exception as e:
        logger.error(f"Failed to load historical data from {file_path}: {e}")
    
    return candles


def save_candles_to_csv(candles: List[Candle], file_path: str):
    """Save candles to CSV file"""
    try:
        data = []
        for candle in candles:
            data.append({
                'timestamp': candle.timestamp.isoformat(),
                'symbol': candle.symbol,
                'open': candle.open,
                'high': candle.high,
                'low': candle.low,
                'close': candle.close,
                'volume': candle.volume
            })
        
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        
        logger.info(f"Saved {len(candles)} candles to {file_path}")
        
    except Exception as e:
        logger.error(f"Failed to save candles to {file_path}: {e}")


def generate_sample_data(symbol: str, days: int = 30, start_price: float = 100.0) -> List[Candle]:
    """Generate sample candlestick data for testing"""
    candles = []
    current_price = start_price
    start_date = datetime.now() - timedelta(days=days)
    
    # Generate data for each minute
    for i in range(days * 24 * 60):  # minutes in specified days
        timestamp = start_date + timedelta(minutes=i)
        
        # Skip weekends (simplified)
        if timestamp.weekday() >= 5:
            continue
        
        # Skip non-trading hours (simplified: 9:30 AM - 4:00 PM EST)
        if timestamp.hour < 9 or (timestamp.hour == 9 and timestamp.minute < 30) or timestamp.hour >= 16:
            continue
        
        # Generate random price movement
        import random
        price_change = random.uniform(-0.5, 0.5)  # Max 0.5% change per minute
        price_change_amount = current_price * (price_change / 100)
        
        # Calculate OHLC
        open_price = current_price
        close_price = current_price + price_change_amount
        
        # High and low with some randomness
        high_price = max(open_price, close_price) + random.uniform(0, abs(price_change_amount) * 0.5)
        low_price = min(open_price, close_price) - random.uniform(0, abs(price_change_amount) * 0.5)
        
        volume = random.randint(1000, 10000)
        
        candle = Candle(
            symbol=symbol,
            timestamp=timestamp,
            open=round(open_price, 2),
            high=round(high_price, 2),
            low=round(low_price, 2),
            close=round(close_price, 2),
            volume=volume
        )
        
        candles.append(candle)
        current_price = close_price
    
    logger.info(f"Generated {len(candles)} sample candles for {symbol}")
    return candles


def create_sample_backtest_data():
    """Create sample data for backtesting"""
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
    start_prices = {"AAPL": 150.0, "MSFT": 300.0, "GOOGL": 2500.0, "TSLA": 200.0}
    
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    historical_data = {}
    
    for symbol in symbols:
        candles = generate_sample_data(symbol, days=30, start_price=start_prices[symbol])
        
        # Save to CSV
        csv_file = data_dir / f"{symbol}_sample_data.csv"
        save_candles_to_csv(candles, str(csv_file))
        
        # Convert to dict format for backtesting
        historical_data[symbol] = [
            {
                "timestamp": candle.timestamp.isoformat(),
                "open": candle.open,
                "high": candle.high,
                "low": candle.low,
                "close": candle.close,
                "volume": candle.volume
            }
            for candle in candles
        ]
    
    # Save combined data for backtesting
    backtest_file = data_dir / "backtest_data.json"
    with open(backtest_file, 'w') as f:
        json.dump(historical_data, f, indent=2)
    
    logger.info(f"Created sample backtest data in {data_dir}")
    return historical_data


def analyze_strategy_performance(trades_history: List[Dict]) -> Dict:
    """Analyze strategy performance from trades history"""
    if not trades_history:
        return {"error": "No trades to analyze"}
    
    df = pd.DataFrame(trades_history)
    
    # Basic statistics
    total_trades = len(df)
    winning_trades = len(df[df['pnl'] > 0])
    losing_trades = len(df[df['pnl'] < 0])
    
    win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
    
    # P&L statistics
    total_pnl = df['pnl'].sum()
    avg_win = df[df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
    avg_loss = df[df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
    
    largest_win = df['pnl'].max()
    largest_loss = df['pnl'].min()
    
    # Risk metrics
    profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
    
    # Duration analysis
    avg_duration = df['duration'].mean() if 'duration' in df.columns else 0
    
    # Monthly breakdown
    df['month'] = pd.to_datetime(df['entry_time']).dt.to_period('M')
    monthly_pnl = df.groupby('month')['pnl'].sum().to_dict()
    
    return {
        "summary": {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "total_pnl": total_pnl,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "largest_win": largest_win,
            "largest_loss": largest_loss,
            "profit_factor": profit_factor,
            "avg_duration_minutes": avg_duration
        },
        "monthly_pnl": {str(k): v for k, v in monthly_pnl.items()}
    }


def validate_configuration():
    """Validate trading bot configuration"""
    from config import config
    
    issues = []
    
    # Check required settings
    if config.capital <= 0:
        issues.append("Capital must be greater than 0")
    
    if config.max_position_percent <= 0 or config.max_position_percent > 100:
        issues.append("Max position percent must be between 0 and 100")
    
    if config.stop_loss_percent <= 0:
        issues.append("Stop loss percent must be greater than 0")
    
    if config.take_profit_percent <= 0:
        issues.append("Take profit percent must be greater than 0")
    
    if config.rising_candles_trigger < 1:
        issues.append("Rising candles trigger must be at least 1")
    
    if config.falling_candles_exit < 1:
        issues.append("Falling candles exit must be at least 1")
    
    # Check IB connection settings
    if not config.ib_host:
        issues.append("IB host is required")
    
    if config.ib_port not in [7497, 4002]:
        logger.warning("IB port should typically be 7497 (TWS) or 4002 (Gateway)")
    
    # Check risk settings
    max_position_value = config.max_position_size
    max_loss_per_position = max_position_value * (config.stop_loss_percent / 100)
    
    if max_loss_per_position > config.max_daily_loss:
        issues.append(f"Max loss per position (${max_loss_per_position:.2f}) exceeds daily loss limit (${config.max_daily_loss:.2f})")
    
    if issues:
        logger.error("Configuration validation failed:")
        for issue in issues:
            logger.error(f"  - {issue}")
        return False
    else:
        logger.info("Configuration validation passed")
        return True


def create_test_environment():
    """Create test environment with sample data"""
    logger.info("Creating test environment...")
    
    # Create directories
    for dir_name in ["data", "reports", "logs"]:
        Path(dir_name).mkdir(exist_ok=True)
    
    # Create sample data
    create_sample_backtest_data()
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            logger.info("Created .env file from .env.example")
        else:
            logger.warning(".env.example not found, please create .env manually")
    
    logger.info("Test environment created successfully")


if __name__ == "__main__":
    # Run utility functions for testing
    create_test_environment()
    validate_configuration()
