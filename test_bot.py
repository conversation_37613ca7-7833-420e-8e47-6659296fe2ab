"""
Test script for the trading bot
"""
import asyncio
from datetime import datetime, timedelta
from loguru import logger

from strategy import ShortStrategy
from risk_manager import RiskManager
from models import Candle, Portfolio, Position, OrderSide
from backtesting import BacktestEngine
from utils import generate_sample_data, validate_configuration, create_test_environment
from config import config


def test_strategy():
    """Test the short strategy"""
    logger.info("Testing short strategy...")
    
    strategy = ShortStrategy()
    symbol = "AAPL"
    
    # Create test candles - 3 rising candles
    base_time = datetime.now()
    test_candles = [
        Candle(symbol=symbol, timestamp=base_time, open=100, high=101, low=99.5, close=100.5, volume=1000),
        Candle(symbol=symbol, timestamp=base_time + timedelta(minutes=1), open=100.5, high=102, low=100, close=101.5, volume=1000),
        Candle(symbol=symbol, timestamp=base_time + timedelta(minutes=2), open=101.5, high=103, low=101, close=102.5, volume=1000),
    ]
    
    # Add candles to strategy
    for candle in test_candles:
        strategy.add_candle(candle)
    
    # Test entry signal
    signals = strategy.analyze_symbol(symbol, 102.5)
    
    if signals and signals[0].signal_type == "SHORT_ENTRY":
        logger.info("✓ Entry signal generated correctly")
    else:
        logger.error("✗ Entry signal not generated")
    
    # Test exit signal - add 2 falling candles
    falling_candles = [
        Candle(symbol=symbol, timestamp=base_time + timedelta(minutes=3), open=102.5, high=102.5, low=101, close=101.5, volume=1000),
        Candle(symbol=symbol, timestamp=base_time + timedelta(minutes=4), open=101.5, high=101.5, low=100, close=100.5, volume=1000),
    ]
    
    # Simulate having a position
    position = Position(
        id="test_position",
        symbol=symbol,
        side=OrderSide.SELL,
        quantity=10,
        entry_price=102.5,
        current_price=100.5
    )
    strategy.add_position(position)
    
    for candle in falling_candles:
        strategy.add_candle(candle)
    
    exit_signals = strategy.analyze_symbol(symbol, 100.5)
    
    if exit_signals and any(s.signal_type == "SHORT_EXIT" for s in exit_signals):
        logger.info("✓ Exit signal generated correctly")
    else:
        logger.error("✗ Exit signal not generated")


def test_risk_manager():
    """Test the risk manager"""
    logger.info("Testing risk manager...")
    
    risk_manager = RiskManager()
    
    # Test position with profit
    position = Position(
        id="test_position",
        symbol="AAPL",
        side=OrderSide.SELL,
        quantity=10,
        entry_price=100.0,
        current_price=90.0  # 10% profit for short position
    )
    
    # Test take profit (only if not using trailing exit)
    if not config.use_trailing_exit and config.take_profit_percent > 0:
        if risk_manager.should_take_profit(position, 90.0):
            logger.info("✓ Take profit triggered correctly")
        else:
            logger.error("✗ Take profit not triggered")
    else:
        logger.info("✓ Trailing exit mode - no fixed take profit")
    
    # Test stop loss
    position.current_price = 105.0  # 5% loss for short position
    if risk_manager.should_stop_loss(position, 105.0):
        logger.info("✓ Stop loss triggered correctly")
    else:
        logger.error("✗ Stop loss not triggered")
    
    # Test portfolio limits
    portfolio = Portfolio(cash=1000.0)
    can_trade, reason = risk_manager.can_open_new_position(portfolio, 200.0)  # 20% of capital
    
    if can_trade:
        logger.info("✓ Position size validation passed")
    else:
        logger.error(f"✗ Position size validation failed: {reason}")


def test_backtest():
    """Test backtesting functionality"""
    logger.info("Testing backtesting...")
    
    # Generate sample data
    symbol = "AAPL"
    candles = generate_sample_data(symbol, days=5, start_price=150.0)
    
    if len(candles) < 100:
        logger.warning("Not enough sample data generated for meaningful backtest")
        return
    
    # Run backtest
    backtest_engine = BacktestEngine()
    historical_data = {symbol: candles}
    
    results = backtest_engine.run_backtest(historical_data)
    
    if "error" in results:
        logger.error(f"✗ Backtest failed: {results['error']}")
        return
    
    logger.info("✓ Backtest completed successfully")
    logger.info(f"  Total trades: {results['trades']['total_trades']}")
    logger.info(f"  Win rate: {results['trades']['win_rate']:.2f}%")
    logger.info(f"  Total P&L: ${results['summary']['total_pnl']:.2f}")
    logger.info(f"  Total return: {results['summary']['total_return']:.2f}%")


async def test_ib_connection():
    """Test IB connection (requires IB to be running)"""
    logger.info("Testing IB connection...")
    
    try:
        from ib_connector import IBConnector
        
        connector = IBConnector()
        connected = await connector.connect()
        
        if connected:
            logger.info("✓ IB connection successful")
            
            # Test getting contract details
            contract = await connector.get_contract_details("AAPL")
            if contract:
                logger.info("✓ Contract details retrieved")
            else:
                logger.warning("✗ Failed to get contract details")
            
            await connector.disconnect()
        else:
            logger.warning("✗ IB connection failed (make sure TWS/Gateway is running)")
    
    except Exception as e:
        logger.warning(f"✗ IB connection test failed: {e}")


def run_all_tests():
    """Run all tests"""
    logger.info("=== RUNNING TRADING BOT TESTS ===")
    
    # Setup test environment
    create_test_environment()
    
    # Validate configuration
    if not validate_configuration():
        logger.error("Configuration validation failed, stopping tests")
        return
    
    # Run tests
    test_strategy()
    test_risk_manager()
    test_backtest()
    
    logger.info("=== TESTS COMPLETED ===")


async def run_async_tests():
    """Run async tests"""
    await test_ib_connection()


if __name__ == "__main__":
    # Setup logging
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}"
    )
    
    # Run synchronous tests
    run_all_tests()
    
    # Run async tests
    asyncio.run(run_async_tests())
    
    logger.info("\n=== TEST SUMMARY ===")
    logger.info("If all tests passed, you can proceed with:")
    logger.info("1. Configure your .env file")
    logger.info("2. Start IB TWS/Gateway")
    logger.info("3. Run: python main.py backtest  # for testing")
    logger.info("4. Run: python main.py live     # for live trading")
