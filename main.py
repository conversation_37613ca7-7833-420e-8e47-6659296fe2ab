"""
Main entry point for the trading bot
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger

from trading_bot import TradingBot
from backtesting import BacktestEngine
from config import config


def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Console logging
    logger.add(
        sys.stdout,
        level=config.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # File logging
    logger.add(
        config.log_file,
        level=config.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    logger.info("Logging setup complete")


async def run_live_trading():
    """Run live trading"""
    # Default symbols for demo - you can modify these
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
    
    bot = TradingBot(symbols)
    
    try:
        # Initialize bot
        if not await bot.initialize():
            logger.error("Failed to initialize trading bot")
            return
        
        # Run trading loop
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await bot.shutdown()


async def run_backtest():
    """Run backtest on historical data"""
    logger.info("Starting backtest mode...")
    
    # Example historical data structure
    # In real implementation, you would load this from files or API
    historical_data = {
        "AAPL": [
            # Example data - replace with real historical data
            {
                "timestamp": "2024-01-01T09:30:00",
                "open": 150.0,
                "high": 152.0,
                "low": 149.0,
                "close": 151.0,
                "volume": 1000000
            },
            # Add more historical data here...
        ]
    }
    
    if not historical_data["AAPL"]:
        logger.error("No historical data available for backtesting")
        logger.info("Please add historical data to the historical_data dictionary in main.py")
        return
    
    backtest_engine = BacktestEngine()
    
    # Convert to Candle objects
    candle_data = {}
    for symbol, data in historical_data.items():
        candle_data[symbol] = backtest_engine.load_historical_data(symbol, data)
    
    # Run backtest
    results = backtest_engine.run_backtest(candle_data)
    
    # Print results
    if "error" in results:
        logger.error(f"Backtest error: {results['error']}")
        return
    
    logger.info("=== BACKTEST RESULTS ===")
    logger.info(f"Initial Capital: ${results['summary']['initial_capital']:,.2f}")
    logger.info(f"Final Equity: ${results['summary']['final_equity']:,.2f}")
    logger.info(f"Total Return: {results['summary']['total_return']:.2f}%")
    logger.info(f"Total P&L: ${results['summary']['total_pnl']:,.2f}")
    logger.info(f"Max Drawdown: {results['summary']['max_drawdown']:.2f}%")
    logger.info(f"Sharpe Ratio: {results['summary']['sharpe_ratio']:.2f}")
    
    logger.info(f"Total Trades: {results['trades']['total_trades']}")
    logger.info(f"Win Rate: {results['trades']['win_rate']:.2f}%")
    logger.info(f"Avg Win: ${results['trades']['avg_win']:.2f}")
    logger.info(f"Avg Loss: ${results['trades']['avg_loss']:.2f}")
    logger.info(f"Profit Factor: {results['trades']['profit_factor']:.2f}")
    
    # Save results to file
    import json
    results_file = Path("reports/backtest_results.json")
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"Backtest results saved to {results_file}")


def print_usage():
    """Print usage information"""
    print("""
Trading Bot Usage:

python main.py [mode]

Modes:
  live      - Run live trading (default)
  backtest  - Run backtest on historical data
  help      - Show this help message

Configuration:
  - Copy .env.example to .env and configure your settings
  - Make sure Interactive Brokers TWS or Gateway is running
  - Configure your symbols in the run_live_trading() function

Examples:
  python main.py live      # Run live trading
  python main.py backtest  # Run backtest
  python main.py help      # Show help
    """)


async def main():
    """Main function"""
    setup_logging()
    
    # Parse command line arguments
    mode = "live"  # default mode
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    
    if mode == "help":
        print_usage()
        return
    elif mode == "backtest":
        await run_backtest()
    elif mode == "live":
        logger.info("Starting in live trading mode...")
        logger.info(f"Configuration: Capital=${config.capital}, Max Position={config.max_position_percent}%")
        logger.info(f"Risk Management: Stop Loss={config.stop_loss_percent}%, Take Profit={config.take_profit_percent}%")
        logger.info(f"Strategy: {config.rising_candles_trigger} rising candles → SHORT, {config.falling_candles_exit} falling candles → EXIT")
        
        await run_live_trading()
    else:
        logger.error(f"Unknown mode: {mode}")
        print_usage()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application terminated by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)
