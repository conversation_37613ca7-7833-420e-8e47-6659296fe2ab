"""
Main trading bot implementation
"""
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional
import signal
import sys
from loguru import logger

from ib_connector import IBConnector
from strategy import ShortStrategy
from risk_manager import RiskManager
from reporting import ReportGenerator, PerformanceTracker
from models import Portfolio, Position, OrderSide, PositionStatus, Candle
from config import config


class TradingBot:
    """Main trading bot class"""
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.ib_connector = IBConnector()
        self.strategy = ShortStrategy()
        self.risk_manager = RiskManager()
        self.report_generator = ReportGenerator()
        self.performance_tracker = PerformanceTracker()
        
        self.portfolio = Portfolio(cash=config.capital)
        self.running = False
        self.last_report_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize the trading bot"""
        logger.info("Initializing trading bot...")
        
        # Connect to Interactive Brokers
        if not await self.ib_connector.connect():
            logger.error("Failed to connect to Interactive Brokers")
            return False
        
        # Load initial portfolio state
        await self.load_portfolio_state()
        
        # Validate symbols
        for symbol in self.symbols:
            contract = await self.ib_connector.get_contract_details(symbol)
            if not contract:
                logger.error(f"Failed to get contract details for {symbol}")
                return False
        
        logger.info(f"Trading bot initialized successfully for symbols: {self.symbols}")
        return True
    
    async def load_portfolio_state(self):
        """Load current portfolio state from IB"""
        try:
            # Get account summary
            account_summary = await self.ib_connector.get_account_summary()
            if 'TotalCashValue' in account_summary:
                self.portfolio.cash = account_summary['TotalCashValue']
            
            # Get current positions
            ib_positions = await self.ib_connector.get_positions()
            for ib_pos in ib_positions:
                if ib_pos.symbol in self.symbols:
                    self.portfolio.add_position(ib_pos)
                    self.strategy.add_position(ib_pos)
            
            logger.info(f"Loaded portfolio: Cash=${self.portfolio.cash:.2f}, Positions={len(self.portfolio.positions)}")
            
        except Exception as e:
            logger.error(f"Failed to load portfolio state: {e}")
    
    async def get_latest_candles(self, symbol: str, count: int = 10) -> List[Candle]:
        """Get latest candles for a symbol"""
        try:
            candles = await self.ib_connector.get_historical_data(
                symbol=symbol,
                duration="1 D",
                bar_size="1 min"
            )
            return candles[-count:] if len(candles) >= count else candles
        except Exception as e:
            logger.error(f"Failed to get candles for {symbol}: {e}")
            return []
    
    async def update_position_prices(self):
        """Update current prices for all open positions"""
        for position in self.portfolio.open_positions:
            try:
                current_price = await self.ib_connector.get_current_price(position.symbol)
                if current_price:
                    position.current_price = current_price
            except Exception as e:
                logger.error(f"Failed to update price for {position.symbol}: {e}")
    
    async def check_risk_management(self):
        """Check risk management for all positions"""
        positions_to_close = []
        
        for position in self.portfolio.open_positions:
            risk_action = self.risk_manager.check_position_risk(position, position.current_price)
            
            if risk_action in ["STOP_LOSS", "TAKE_PROFIT"]:
                positions_to_close.append((position, risk_action))
        
        # Close positions that need risk management
        for position, reason in positions_to_close:
            await self.close_position(position, reason)
    
    async def close_position(self, position: Position, reason: str):
        """Close a position"""
        try:
            # Place market order to close position
            close_side = OrderSide.BUY if position.side == OrderSide.SELL else OrderSide.SELL
            order = await self.ib_connector.place_market_order(
                symbol=position.symbol,
                side=close_side,
                quantity=position.quantity
            )
            
            if order:
                # Update position
                position.status = PositionStatus.CLOSED
                position.closed_at = datetime.now()
                
                # Calculate P&L
                if position.side == OrderSide.SELL:  # Short position
                    pnl = (position.entry_price - position.current_price) * position.quantity
                else:  # Long position
                    pnl = (position.current_price - position.entry_price) * position.quantity
                
                position.pnl = pnl
                self.portfolio.total_pnl += pnl
                
                # Update risk manager
                self.risk_manager.update_daily_pnl(pnl)
                
                # Remove from strategy tracking
                self.strategy.remove_position(position.symbol)
                
                logger.info(f"Closed position {position.symbol}: P&L=${pnl:.2f} ({reason})")
            
        except Exception as e:
            logger.error(f"Failed to close position {position.symbol}: {e}")
    
    async def execute_signal(self, signal, current_price: float):
        """Execute a trading signal"""
        try:
            if signal.signal_type == "SHORT_ENTRY":
                # Validate signal with risk manager
                can_trade, reason = self.risk_manager.validate_trade_signal(signal, self.portfolio)
                if not can_trade:
                    logger.warning(f"Signal rejected: {reason}")
                    return
                
                # Calculate position size
                position_size = self.strategy.calculate_position_size(signal.symbol, current_price)
                
                # Place short order
                order = await self.ib_connector.place_market_order(
                    symbol=signal.symbol,
                    side=OrderSide.SELL,
                    quantity=position_size
                )
                
                if order:
                    # Create position
                    position = Position(
                        id=f"{signal.symbol}_{datetime.now().isoformat()}",
                        symbol=signal.symbol,
                        side=OrderSide.SELL,
                        quantity=position_size,
                        entry_price=current_price,
                        current_price=current_price
                    )
                    
                    # Set stop loss and take profit
                    position.stop_loss = self.risk_manager.calculate_stop_loss_price(position)
                    position.take_profit = self.risk_manager.calculate_take_profit_price(position)
                    
                    # Add to portfolio and strategy
                    self.portfolio.add_position(position)
                    self.strategy.add_position(position)
                    
                    logger.info(f"Opened SHORT position: {signal.symbol} @ {current_price}, size: {position_size}")
            
            elif signal.signal_type == "SHORT_EXIT":
                # Find and close matching position
                for position in self.portfolio.open_positions:
                    if position.symbol == signal.symbol and position.side == OrderSide.SELL:
                        await self.close_position(position, "STRATEGY_EXIT")
                        break
        
        except Exception as e:
            logger.error(f"Failed to execute signal {signal.signal_type} for {signal.symbol}: {e}")
    
    async def process_symbol(self, symbol: str):
        """Process a single symbol"""
        try:
            # Get latest candles
            candles = await self.get_latest_candles(symbol, 5)
            if not candles:
                return
            
            # Add candles to strategy
            for candle in candles:
                self.strategy.add_candle(candle)
            
            # Get current price
            current_price = await self.ib_connector.get_current_price(symbol)
            if not current_price:
                return
            
            # Generate signals
            signals = self.strategy.analyze_symbol(symbol, current_price)
            
            # Execute signals
            for signal in signals:
                await self.execute_signal(signal, current_price)
        
        except Exception as e:
            logger.error(f"Error processing symbol {symbol}: {e}")
    
    async def generate_daily_report(self):
        """Generate and send daily report"""
        try:
            # Update portfolio value
            await self.update_position_prices()
            
            # Get risk summary
            risk_summary = self.risk_manager.get_risk_summary(self.portfolio)
            
            # Generate report
            report = self.report_generator.generate_daily_report(
                portfolio=self.portfolio,
                signals=self.strategy.signals,
                risk_summary=risk_summary
            )
            
            # Save report
            self.report_generator.save_daily_report(report)
            
            # Generate HTML report
            html_path = self.report_generator.generate_html_report(report)
            
            # Send email report
            self.report_generator.send_email_report(report, html_path)
            
            # Update performance tracking
            self.performance_tracker.add_daily_performance(report)
            
            logger.info("Daily report generated and sent")
            
        except Exception as e:
            logger.error(f"Failed to generate daily report: {e}")
    
    async def run(self):
        """Main trading loop"""
        logger.info("Starting trading bot...")
        self.running = True
        
        while self.running:
            try:
                # Update position prices
                await self.update_position_prices()
                
                # Check risk management
                await self.check_risk_management()
                
                # Process each symbol
                for symbol in self.symbols:
                    if not self.running:
                        break
                    await self.process_symbol(symbol)
                
                # Check if we need to generate daily report
                now = datetime.now()
                if now.date() > self.last_report_time.date():
                    await self.generate_daily_report()
                    self.last_report_time = now
                
                # Wait before next iteration
                await asyncio.sleep(60)  # 1 minute interval
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(60)
        
        logger.info("Trading bot stopped")
    
    async def shutdown(self):
        """Shutdown the trading bot"""
        logger.info("Shutting down trading bot...")
        
        # Generate final report
        await self.generate_daily_report()
        
        # Disconnect from IB
        await self.ib_connector.disconnect()
        
        logger.info("Trading bot shutdown complete")
