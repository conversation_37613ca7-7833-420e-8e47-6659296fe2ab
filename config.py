"""
Configuration management for the trading bot
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

load_dotenv()


class TradingConfig(BaseSettings):
    """Trading bot configuration"""
    
    # IB API Configuration
    ib_host: str = Field(default="127.0.0.1", env="IB_HOST")
    ib_port: int = Field(default=7497, env="IB_PORT")  # 7497 for TWS, 4002 for Gateway
    ib_client_id: int = Field(default=1, env="IB_CLIENT_ID")
    
    # Capital Management
    capital: float = Field(default=1000.0, env="CAPITAL")
    max_position_percent: float = Field(default=20.0, env="MAX_POSITION_PERCENT")
    
    # Risk Management
    stop_loss_percent: float = Field(default=5.0, env="STOP_LOSS_PERCENT")
    take_profit_percent: float = Field(default=10.0, env="TAKE_PROFIT_PERCENT")
    max_daily_loss: float = Field(default=50.0, env="MAX_DAILY_LOSS")
    max_positions: int = Field(default=5, env="MAX_POSITIONS")
    
    # Strategy Parameters
    rising_candles_trigger: int = Field(default=3, env="RISING_CANDLES_TRIGGER")
    falling_candles_exit: int = Field(default=2, env="FALLING_CANDLES_EXIT")
    timeframe_minutes: int = Field(default=1, env="TIMEFRAME_MINUTES")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="trading_bot.log", env="LOG_FILE")
    
    # Reporting
    report_email: Optional[str] = Field(default=None, env="REPORT_EMAIL")
    smtp_server: Optional[str] = Field(default=None, env="SMTP_SERVER")
    smtp_port: Optional[int] = Field(default=587, env="SMTP_PORT")
    smtp_username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    
    @property
    def max_position_size(self) -> float:
        """Calculate maximum position size in dollars"""
        return self.capital * (self.max_position_percent / 100)
    
    @property
    def stop_loss_amount(self) -> float:
        """Calculate stop loss amount in dollars"""
        return self.max_position_size * (self.stop_loss_percent / 100)
    
    @property
    def take_profit_amount(self) -> float:
        """Calculate take profit amount in dollars"""
        return self.max_position_size * (self.take_profit_percent / 100)
    
    class Config:
        env_file = ".env"


# Global config instance
config = TradingConfig()
