"""
Short trading strategy implementation
"""
from datetime import datetime, timedelta
from typing import List, Optional, Dict
from loguru import logger
import asyncio

from models import Candle, TradingSignal, Position, OrderSide
from config import config


class ShortStrategy:
    """
    Short strategy based on consecutive rising candles
    Entry: 3 consecutive rising candles -> SHORT
    Exit: 2 consecutive falling candles -> CLOSE
    """
    
    def __init__(self):
        self.candle_history: Dict[str, List[Candle]] = {}
        self.signals: List[TradingSignal] = []
        self.active_positions: Dict[str, Position] = {}
        
    def add_candle(self, candle: Candle):
        """Add a new candle to the history"""
        symbol = candle.symbol
        
        if symbol not in self.candle_history:
            self.candle_history[symbol] = []
        
        self.candle_history[symbol].append(candle)
        
        # Keep only last 10 candles for efficiency
        if len(self.candle_history[symbol]) > 10:
            self.candle_history[symbol] = self.candle_history[symbol][-10:]
        
        logger.debug(f"Added candle for {symbol}: O:{candle.open} H:{candle.high} L:{candle.low} C:{candle.close}")
    
    def get_recent_candles(self, symbol: str, count: int = 5) -> List[Candle]:
        """Get the most recent candles for a symbol"""
        if symbol not in self.candle_history:
            return []
        
        return self.candle_history[symbol][-count:] if len(self.candle_history[symbol]) >= count else self.candle_history[symbol]
    
    def check_consecutive_rising_candles(self, symbol: str, count: int = 3) -> bool:
        """Check if there are consecutive rising candles"""
        recent_candles = self.get_recent_candles(symbol, count)
        
        if len(recent_candles) < count:
            return False
        
        # Check if all recent candles are green (rising)
        for candle in recent_candles:
            if not candle.is_green:
                return False
        
        # Additional check: each candle should close higher than the previous
        for i in range(1, len(recent_candles)):
            if recent_candles[i].close <= recent_candles[i-1].close:
                return False
        
        logger.info(f"Detected {count} consecutive rising candles for {symbol}")
        return True
    
    def check_consecutive_falling_candles(self, symbol: str, count: int = 2) -> bool:
        """Check if there are consecutive falling candles"""
        recent_candles = self.get_recent_candles(symbol, count)
        
        if len(recent_candles) < count:
            return False
        
        # Check if all recent candles are red (falling)
        for candle in recent_candles:
            if not candle.is_red:
                return False
        
        # Additional check: each candle should close lower than the previous
        for i in range(1, len(recent_candles)):
            if recent_candles[i].close >= recent_candles[i-1].close:
                return False
        
        logger.info(f"Detected {count} consecutive falling candles for {symbol}")
        return True
    
    def generate_entry_signal(self, symbol: str, current_price: float) -> Optional[TradingSignal]:
        """Generate entry signal for short position"""
        if self.check_consecutive_rising_candles(symbol, config.rising_candles_trigger):
            # Check if we don't already have a position in this symbol
            if symbol not in self.active_positions:
                signal = TradingSignal(
                    symbol=symbol,
                    signal_type="SHORT_ENTRY",
                    price=current_price,
                    confidence=1.0,
                    reason=f"{config.rising_candles_trigger} consecutive rising candles detected"
                )
                
                self.signals.append(signal)
                logger.info(f"Generated SHORT_ENTRY signal for {symbol} at {current_price}")
                return signal
        
        return None
    
    def generate_exit_signal(self, symbol: str, current_price: float) -> Optional[TradingSignal]:
        """Generate exit signal for short position"""
        # Only generate exit signal if we have an active position
        if symbol in self.active_positions:
            position = self.active_positions[symbol]

            # Check if we should exit based on trailing strategy
            if config.use_trailing_exit:
                if self.check_consecutive_falling_candles(symbol, config.falling_candles_exit):
                    # Calculate current profit
                    unrealized_pnl = (position.entry_price - current_price) * position.quantity
                    profit_percent = (unrealized_pnl / (position.entry_price * position.quantity)) * 100

                    signal = TradingSignal(
                        symbol=symbol,
                        signal_type="SHORT_EXIT",
                        price=current_price,
                        confidence=1.0,
                        reason=f"Trailing exit: {config.falling_candles_exit} falling candles, profit: {profit_percent:.2f}%"
                    )

                    self.signals.append(signal)
                    logger.info(f"Generated TRAILING_EXIT signal for {symbol} at {current_price} (profit: {profit_percent:.2f}%)")
                    return signal
            else:
                # Original fixed exit logic
                if self.check_consecutive_falling_candles(symbol, config.falling_candles_exit):
                    signal = TradingSignal(
                        symbol=symbol,
                        signal_type="SHORT_EXIT",
                        price=current_price,
                        confidence=1.0,
                        reason=f"{config.falling_candles_exit} consecutive falling candles detected"
                    )

                    self.signals.append(signal)
                    logger.info(f"Generated SHORT_EXIT signal for {symbol} at {current_price}")
                    return signal

        return None
    
    def analyze_symbol(self, symbol: str, current_price: float) -> List[TradingSignal]:
        """Analyze a symbol and generate trading signals"""
        signals = []
        
        # Check for entry signal
        entry_signal = self.generate_entry_signal(symbol, current_price)
        if entry_signal:
            signals.append(entry_signal)
        
        # Check for exit signal
        exit_signal = self.generate_exit_signal(symbol, current_price)
        if exit_signal:
            signals.append(exit_signal)
        
        return signals
    
    def add_position(self, position: Position):
        """Add an active position to track"""
        self.active_positions[position.symbol] = position
        logger.info(f"Added position to track: {position.symbol} - {position.side}")
    
    def remove_position(self, symbol: str):
        """Remove a position from tracking"""
        if symbol in self.active_positions:
            del self.active_positions[symbol]
            logger.info(f"Removed position from tracking: {symbol}")
    
    def get_active_positions(self) -> Dict[str, Position]:
        """Get all active positions"""
        return self.active_positions.copy()
    
    def calculate_position_size(self, symbol: str, current_price: float) -> int:
        """Calculate position size based on risk management"""
        max_position_value = config.max_position_size
        
        # Calculate number of shares we can afford
        shares = int(max_position_value / current_price)
        
        # Ensure minimum position size
        if shares < 1:
            shares = 1
        
        logger.info(f"Calculated position size for {symbol}: {shares} shares (${shares * current_price:.2f})")
        return shares
    
    def should_enter_position(self, symbol: str) -> bool:
        """Check if we should enter a new position"""
        # Don't enter if we already have a position
        if symbol in self.active_positions:
            return False
        
        # Check if we haven't exceeded maximum positions
        if len(self.active_positions) >= config.max_positions:
            logger.warning(f"Maximum positions ({config.max_positions}) reached, skipping entry")
            return False
        
        return True
    
    def get_strategy_stats(self) -> Dict[str, any]:
        """Get strategy statistics"""
        return {
            "active_positions": len(self.active_positions),
            "total_signals": len(self.signals),
            "symbols_tracked": len(self.candle_history),
            "recent_signals": [
                {
                    "symbol": signal.symbol,
                    "type": signal.signal_type,
                    "price": signal.price,
                    "timestamp": signal.timestamp.isoformat()
                }
                for signal in self.signals[-5:]  # Last 5 signals
            ]
        }
