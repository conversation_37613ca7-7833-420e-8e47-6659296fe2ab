# RoboObchod - <PERSON>k<PERSON> Obchodní Robot

Automatický obchodní robot pro short strategii na Interactive Brokers platformě.

## Funkce

- **Short Strategie**: Vstup při 3 po sobě rostoucích sví<PERSON>, výstup při 2 klesajících
- **Trailing Exit**: Neomezený profit - pozice běží dokud trend pokračuje
- **Risk Management**: Stop-loss 5% pro ochranu před ztrátami
- **Portfolio Management**: Maximálně 20% kapitálu na pozici
- **Reporting**: Denní reporty s analýzou výkonnosti
- **Backtesting**: Testování strategie na historických datech
- **24/7 Obchodování**: Kontinuální monitoring trhů

## Požadavky

- Python 3.8+
- Interactive Brokers TWS nebo Gateway
- Demo nebo live účet u Interactive Brokers

## Instalace

1. **Klonování projektu**:
```bash
git clone <repository-url>
cd robobchod
```

2. **Instalace závislostí**:
```bash
pip install -r requirements.txt
```

3. **Konfigurace**:
```bash
cp .env.example .env
# Upravte .env soubor podle vašich potřeb
```

4. **Spuštění Interactive Brokers**:
   - Spusťte TWS (Trader Workstation) nebo Gateway
   - Povolte API připojení v nastavení
   - Nastavte port 7497 (TWS) nebo 4002 (Gateway)

## Konfigurace

Upravte `.env` soubor:

```env
# Interactive Brokers API
IB_HOST=127.0.0.1
IB_PORT=7497
IB_CLIENT_ID=1

# Trading nastavení
CAPITAL=1000
MAX_POSITION_PERCENT=20
STOP_LOSS_PERCENT=5
TAKE_PROFIT_PERCENT=0
USE_TRAILING_EXIT=true

# Strategie
RISING_CANDLES_TRIGGER=3
FALLING_CANDLES_EXIT=2
TIMEFRAME_MINUTES=1

# Risk Management
MAX_DAILY_LOSS=50
MAX_POSITIONS=5
```

## Spuštění

### Live Trading
```bash
python main.py live
```

### Backtesting
```bash
python main.py backtest
```

### Nápověda
```bash
python main.py help
```

## Strategie

### Short Strategie s Trailing Exit
1. **Vstupní signál**: 3 po sobě jdoucí rostoucí minutové svíčky
2. **Výstupní signál**: 2 po sobě jdoucí klesající minutové svíčky (trailing exit)
3. **Risk Management**:
   - Stop-loss: 5% nad vstupní cenou (ochrana před ztrátami)
   - Take-profit: NEOMEZENÝ - pozice běží dokud trend pokračuje
   - Výstup pouze při otočení trendu (2 klesající svíčky)

### Portfolio Management
- Maximální pozice: 20% z celkového kapitálu
- Maximální počet pozic: 5 současně
- Maximální denní ztráta: $50

### Dva Režimy Obchodování

**🚀 Režim 1: Neomezený Profit (Doporučený)**
```env
TAKE_PROFIT_PERCENT=0
USE_TRAILING_EXIT=true
```
- Pozice běží dokud trend pokračuje
- Výstup pouze při otočení trendu (2 klesající svíčky)
- Maximalizuje zisky při silných trendech

**⚡ Režim 2: Pevný Take-Profit**
```env
TAKE_PROFIT_PERCENT=10
USE_TRAILING_EXIT=false
```
- Pevný take-profit na 10%
- Rychlejší uzavírání pozic
- Konzervativnější přístup

## Reporting

Robot automaticky generuje:
- **Denní reporty**: JSON a HTML formát
- **Performance tracking**: Sledování výkonnosti v čase
- **Email notifikace**: Volitelné zasílání reportů
- **Grafy výkonnosti**: Vizualizace equity curve

Reporty se ukládají do složky `reports/`.

## Backtesting

Pro testování strategie na historických datech:

1. Připravte historická data ve formátu:
```python
historical_data = {
    "AAPL": [
        {
            "timestamp": "2024-01-01T09:30:00",
            "open": 150.0,
            "high": 152.0,
            "low": 149.0,
            "close": 151.0,
            "volume": 1000000
        },
        # více dat...
    ]
}
```

2. Spusťte backtest:
```bash
python main.py backtest
```

## Struktura Projektu

```
robobchod/
├── main.py              # Hlavní spouštěcí soubor
├── trading_bot.py       # Hlavní třída trading bota
├── config.py            # Konfigurace
├── models.py            # Datové modely
├── strategy.py          # Short strategie
├── risk_manager.py      # Risk management
├── ib_connector.py      # IB API konektor
├── reporting.py         # Reporting systém
├── backtesting.py       # Backtesting engine
├── requirements.txt     # Python závislosti
├── .env.example         # Příklad konfigurace
└── reports/             # Složka pro reporty
```

## Bezpečnost

⚠️ **DŮLEŽITÉ UPOZORNĚNÍ**:
- Začněte vždy s demo účtem
- Testujte strategii důkladně před použitím reálných peněz
- Nastavte rozumné limity rizika
- Monitorujte robota pravidelně

## Doporučené Symboly

Pro short strategii jsou vhodné:
- **Tech akcie**: AAPL, MSFT, GOOGL, TSLA, NVDA
- **ETF**: QQQ, SPY, IWM
- **Volatilní akcie**: Meme stocks, růstové akcie

## Monitoring

Robot poskytuje:
- **Real-time logy**: Sledování všech operací
- **Denní reporty**: Přehled výkonnosti
- **Risk metriky**: Sledování rizika
- **Position tracking**: Přehled otevřených pozic

## Troubleshooting

### Časté problémy:

1. **Nelze se připojit k IB**:
   - Zkontrolujte, zda je TWS/Gateway spuštěn
   - Ověřte API nastavení v TWS
   - Zkontrolujte port a client ID

2. **Žádné obchody**:
   - Zkontrolujte, zda jsou symboly dostupné
   - Ověřte tržní hodiny
   - Zkontrolujte kapitál a limity

3. **Chyby v datech**:
   - Zkontrolujte internetové připojení
   - Ověřte market data subscriptions

## Podpora

Pro podporu a dotazy:
- Zkontrolujte logy v `trading_bot.log`
- Přečtěte si dokumentaci Interactive Brokers API
- Otestujte na demo účtu

## Licence

Tento projekt je určen pouze pro vzdělávací účely. Používejte na vlastní riziko.

## Disclaimer

Obchodování s finančními instrumenty je rizikové. Minulá výkonnost nezaručuje budoucí výsledky. Autor nenese odpovědnost za finanční ztráty.
