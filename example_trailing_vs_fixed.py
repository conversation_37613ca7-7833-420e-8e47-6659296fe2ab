"""
Příklad porovnání Trailing Exit vs Fixed Take-Profit
"""
from datetime import datetime, timedelta
from models import Candle, Position, OrderSide
from strategy import ShortStrategy
from risk_manager import RiskManager
from config import config


def simulate_price_scenario():
    """Simulace cenového scénáře pro demonstraci rozdílu"""
    
    print("=== POROVNÁNÍ TRAILING EXIT vs FIXED TAKE-PROFIT ===\n")
    
    # Scénář: AAPL short pozice
    symbol = "AAPL"
    entry_price = 150.0
    position_size = 10
    
    # Cenový scénář: cena klesá z 150 na 130, pak se odrazí na 135
    price_scenario = [
        (150.0, "Vstup do SHORT pozice"),
        (148.0, "Cena klesá -1.3%"),
        (145.0, "Cena klesá -3.3%"),
        (140.0, "Cena klesá -6.7%"),
        (135.0, "Cena klesá -10.0%"),
        (130.0, "Cena klesá -13.3%"),
        (132.0, "Cena se odrazí +1.5%"),
        (135.0, "Cena roste +3.8%"),
    ]
    
    print("📊 CENOVÝ SCÉNÁŘ:")
    for price, description in price_scenario:
        profit_pct = ((entry_price - price) / entry_price) * 100
        profit_usd = (entry_price - price) * position_size
        print(f"  ${price:6.2f} | {description:25} | Profit: {profit_pct:+6.2f}% (${profit_usd:+6.2f})")
    
    print("\n" + "="*70)
    
    # Simulace Fixed Take-Profit (10%)
    print("\n🎯 REŽIM 1: FIXED TAKE-PROFIT (10%)")
    take_profit_price = entry_price * 0.9  # 10% pod vstupní cenou
    print(f"Take-profit nastavený na: ${take_profit_price:.2f}")
    
    for price, description in price_scenario:
        profit_pct = ((entry_price - price) / entry_price) * 100
        profit_usd = (entry_price - price) * position_size
        
        if price <= take_profit_price:
            print(f"  ${price:6.2f} | ✅ POZICE UZAVŘENA | Profit: {profit_pct:+6.2f}% (${profit_usd:+6.2f})")
            print(f"           | Take-profit dosažen při ${take_profit_price:.2f}")
            break
        else:
            print(f"  ${price:6.2f} | {description:25} | Profit: {profit_pct:+6.2f}% (${profit_usd:+6.2f})")
    
    print(f"\n📈 VÝSLEDEK FIXED: Pozice uzavřena při ${take_profit_price:.2f} s profitem ${(entry_price - take_profit_price) * position_size:.2f}")
    
    # Simulace Trailing Exit
    print("\n🚀 REŽIM 2: TRAILING EXIT (Neomezený profit)")
    print("Pozice se uzavře pouze při 2 klesajících svíčkách")
    
    # Simulujeme svíčky
    candles = [
        Candle(symbol=symbol, timestamp=datetime.now() + timedelta(minutes=i), 
               open=price_scenario[i][0], high=price_scenario[i][0]+0.5, 
               low=price_scenario[i][0]-0.5, close=price_scenario[i][0], volume=1000)
        for i in range(len(price_scenario))
    ]
    
    # Najdeme bod, kde se trend otočí (2 rostoucí svíčky po klesajících)
    exit_point = None
    for i in range(2, len(candles)):
        # Kontrola 2 rostoucích svíček po klesajících
        if (candles[i-1].close > candles[i-2].close and 
            candles[i].close > candles[i-1].close and
            candles[i-2].close < candles[i-3].close if i >= 3 else True):
            exit_point = i-1  # Uzavřeme na začátku růstu
            break
    
    if exit_point:
        exit_price = candles[exit_point].close
        profit_pct = ((entry_price - exit_price) / entry_price) * 100
        profit_usd = (entry_price - exit_price) * position_size
        
        for i, (price, description) in enumerate(price_scenario):
            current_profit_pct = ((entry_price - price) / entry_price) * 100
            current_profit_usd = (entry_price - price) * position_size
            
            if i == exit_point:
                print(f"  ${price:6.2f} | ✅ POZICE UZAVŘENA | Profit: {current_profit_pct:+6.2f}% (${current_profit_usd:+6.2f})")
                print(f"           | Trend se otočil - 2 rostoucí svíčky")
                break
            else:
                print(f"  ${price:6.2f} | {description:25} | Profit: {current_profit_pct:+6.2f}% (${current_profit_usd:+6.2f})")
        
        print(f"\n📈 VÝSLEDEK TRAILING: Pozice uzavřena při ${exit_price:.2f} s profitem ${profit_usd:.2f}")
    else:
        print("Pozice by zůstala otevřená - trend se neotočil")
    
    # Porovnání
    print("\n" + "="*70)
    print("📊 POROVNÁNÍ VÝSLEDKŮ:")
    
    fixed_profit = (entry_price - take_profit_price) * position_size
    trailing_profit = (entry_price - (exit_price if exit_point else price_scenario[-1][0])) * position_size
    
    print(f"Fixed Take-Profit:  ${fixed_profit:+8.2f}")
    print(f"Trailing Exit:      ${trailing_profit:+8.2f}")
    print(f"Rozdíl:             ${trailing_profit - fixed_profit:+8.2f}")
    
    if trailing_profit > fixed_profit:
        print("🏆 TRAILING EXIT vyhrává - zachytil větší část trendu!")
    else:
        print("🏆 FIXED TAKE-PROFIT vyhrává - ochránil před ztrátou zisku!")
    
    print("\n💡 ZÁVĚR:")
    print("- Trailing Exit je lepší při silných trendech")
    print("- Fixed Take-Profit je bezpečnější při volatilních trzích")
    print("- Doporučujeme Trailing Exit pro maximalizaci zisků")


def show_configuration_examples():
    """Ukázka různých konfigurací"""
    
    print("\n" + "="*70)
    print("⚙️  KONFIGURACE PRO RŮZNÉ STYLY OBCHODOVÁNÍ")
    print("="*70)
    
    print("\n🚀 AGRESIVNÍ (Maximální zisky):")
    print("TAKE_PROFIT_PERCENT=0")
    print("USE_TRAILING_EXIT=true")
    print("STOP_LOSS_PERCENT=3")
    print("RISING_CANDLES_TRIGGER=2")
    print("FALLING_CANDLES_EXIT=3")
    
    print("\n⚖️  VYVÁŽENÝ (Doporučený):")
    print("TAKE_PROFIT_PERCENT=0")
    print("USE_TRAILING_EXIT=true") 
    print("STOP_LOSS_PERCENT=5")
    print("RISING_CANDLES_TRIGGER=3")
    print("FALLING_CANDLES_EXIT=2")
    
    print("\n🛡️  KONZERVATIVNÍ (Bezpečný):")
    print("TAKE_PROFIT_PERCENT=8")
    print("USE_TRAILING_EXIT=false")
    print("STOP_LOSS_PERCENT=4")
    print("RISING_CANDLES_TRIGGER=4")
    print("FALLING_CANDLES_EXIT=2")


if __name__ == "__main__":
    simulate_price_scenario()
    show_configuration_examples()
    
    print("\n" + "="*70)
    print("🎯 DOPORUČENÍ:")
    print("1. Začněte s TRAILING EXIT režimem")
    print("2. Testujte na demo účtu")
    print("3. Sledujte výkonnost a upravte podle potřeby")
    print("4. V volatilních trzích zvažte Fixed Take-Profit")
    print("="*70)
