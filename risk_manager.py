"""
Risk management system for the trading bot
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
from loguru import logger

from models import Position, Portfolio, OrderSide, PositionStatus, TradingSignal
from config import config


class RiskManager:
    """Risk management system"""
    
    def __init__(self):
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.last_reset_date = datetime.now().date()
        self.max_drawdown = 0.0
        self.peak_value = config.capital
        
    def reset_daily_stats(self):
        """Reset daily statistics if new day"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.last_reset_date = current_date
            logger.info("Daily statistics reset")
    
    def calculate_stop_loss_price(self, position: Position) -> float:
        """Calculate stop loss price for a position"""
        if position.side == OrderSide.SELL:  # Short position
            # For short positions, stop loss is above entry price
            stop_loss_price = position.entry_price * (1 + config.stop_loss_percent / 100)
        else:  # Long position
            # For long positions, stop loss is below entry price
            stop_loss_price = position.entry_price * (1 - config.stop_loss_percent / 100)
        
        return round(stop_loss_price, 2)
    
    def calculate_take_profit_price(self, position: Position) -> Optional[float]:
        """Calculate take profit price for a position (None if unlimited)"""
        if config.take_profit_percent == 0:
            return None  # Unlimited take profit

        if position.side == OrderSide.SELL:  # Short position
            # For short positions, take profit is below entry price
            take_profit_price = position.entry_price * (1 - config.take_profit_percent / 100)
        else:  # Long position
            # For long positions, take profit is above entry price
            take_profit_price = position.entry_price * (1 + config.take_profit_percent / 100)

        return round(take_profit_price, 2)
    
    def should_stop_loss(self, position: Position, current_price: float) -> bool:
        """Check if position should be stopped out"""
        if position.status != PositionStatus.OPEN:
            return False
        
        stop_loss_price = self.calculate_stop_loss_price(position)
        
        if position.side == OrderSide.SELL:  # Short position
            # Stop loss triggered if price goes above stop loss
            if current_price >= stop_loss_price:
                logger.warning(f"Stop loss triggered for {position.symbol}: {current_price} >= {stop_loss_price}")
                return True
        else:  # Long position
            # Stop loss triggered if price goes below stop loss
            if current_price <= stop_loss_price:
                logger.warning(f"Stop loss triggered for {position.symbol}: {current_price} <= {stop_loss_price}")
                return True
        
        return False
    
    def should_take_profit(self, position: Position, current_price: float) -> bool:
        """Check if position should take profit (only if fixed take-profit is set)"""
        if position.status != PositionStatus.OPEN:
            return False

        # If using trailing exit strategy, don't use fixed take-profit
        if config.use_trailing_exit:
            return False

        take_profit_price = self.calculate_take_profit_price(position)

        # If unlimited take profit, don't trigger
        if take_profit_price is None:
            return False

        if position.side == OrderSide.SELL:  # Short position
            # Take profit triggered if price goes below take profit
            if current_price <= take_profit_price:
                logger.info(f"Take profit triggered for {position.symbol}: {current_price} <= {take_profit_price}")
                return True
        else:  # Long position
            # Take profit triggered if price goes above take profit
            if current_price >= take_profit_price:
                logger.info(f"Take profit triggered for {position.symbol}: {current_price} >= {take_profit_price}")
                return True

        return False
    
    def check_position_risk(self, position: Position, current_price: float) -> Optional[str]:
        """Check if position needs risk management action"""
        if self.should_stop_loss(position, current_price):
            return "STOP_LOSS"
        elif self.should_take_profit(position, current_price):
            return "TAKE_PROFIT"
        return None

    def should_trail_stop(self, position: Position, current_price: float, trailing_percent: float = 2.0) -> bool:
        """Check if position should be closed due to trailing stop"""
        if position.status != PositionStatus.OPEN:
            return False

        # Only for short positions in our strategy
        if position.side == OrderSide.SELL:
            # Calculate unrealized profit
            unrealized_pnl_percent = position.unrealized_pnl_percent

            # If we're in profit and price starts moving against us by trailing_percent
            if unrealized_pnl_percent > 0:  # We're in profit
                # Check if price moved up by trailing_percent from the best price
                # This is a simplified trailing stop - in production you'd track the best price
                if current_price > position.entry_price * (1 + trailing_percent / 100):
                    logger.info(f"Trailing stop triggered for {position.symbol}: price moved up {trailing_percent}% from entry")
                    return True

        return False
    
    def can_open_new_position(self, portfolio: Portfolio, position_value: float) -> Tuple[bool, str]:
        """Check if we can open a new position"""
        self.reset_daily_stats()
        
        # Check daily loss limit
        if self.daily_pnl <= -config.max_daily_loss:
            return False, f"Daily loss limit reached: ${self.daily_pnl:.2f}"
        
        # Check maximum positions
        open_positions = len(portfolio.open_positions)
        if open_positions >= config.max_positions:
            return False, f"Maximum positions limit reached: {open_positions}/{config.max_positions}"
        
        # Check if position size is within limits
        max_position_size = config.max_position_size
        if position_value > max_position_size:
            return False, f"Position size too large: ${position_value:.2f} > ${max_position_size:.2f}"
        
        # Check available cash
        if position_value > portfolio.cash:
            return False, f"Insufficient cash: ${position_value:.2f} > ${portfolio.cash:.2f}"
        
        return True, "OK"
    
    def validate_trade_signal(self, signal: TradingSignal, portfolio: Portfolio) -> Tuple[bool, str]:
        """Validate if a trading signal should be executed"""
        if signal.signal_type == "SHORT_ENTRY":
            position_value = config.max_position_size
            return self.can_open_new_position(portfolio, position_value)
        
        return True, "OK"
    
    def update_daily_pnl(self, pnl_change: float):
        """Update daily P&L"""
        self.reset_daily_stats()
        self.daily_pnl += pnl_change
        self.daily_trades += 1
        
        logger.info(f"Daily P&L updated: ${self.daily_pnl:.2f} (trades: {self.daily_trades})")
    
    def update_drawdown(self, current_portfolio_value: float):
        """Update maximum drawdown tracking"""
        if current_portfolio_value > self.peak_value:
            self.peak_value = current_portfolio_value
        
        current_drawdown = (self.peak_value - current_portfolio_value) / self.peak_value * 100
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
            logger.warning(f"New maximum drawdown: {self.max_drawdown:.2f}%")
    
    def calculate_position_risk_metrics(self, position: Position, current_price: float) -> Dict[str, float]:
        """Calculate risk metrics for a position"""
        position.current_price = current_price
        
        unrealized_pnl = position.unrealized_pnl
        unrealized_pnl_percent = position.unrealized_pnl_percent
        
        stop_loss_price = self.calculate_stop_loss_price(position)
        take_profit_price = self.calculate_take_profit_price(position)
        
        # Calculate potential loss/gain
        if position.side == OrderSide.SELL:  # Short position
            potential_loss = (stop_loss_price - position.entry_price) * position.quantity
            potential_gain = (position.entry_price - take_profit_price) * position.quantity
        else:  # Long position
            potential_loss = (position.entry_price - stop_loss_price) * position.quantity
            potential_gain = (take_profit_price - position.entry_price) * position.quantity
        
        return {
            "unrealized_pnl": unrealized_pnl,
            "unrealized_pnl_percent": unrealized_pnl_percent,
            "stop_loss_price": stop_loss_price,
            "take_profit_price": take_profit_price,
            "potential_loss": potential_loss,
            "potential_gain": potential_gain,
            "risk_reward_ratio": abs(potential_gain / potential_loss) if potential_loss != 0 else 0
        }
    
    def get_risk_summary(self, portfolio: Portfolio) -> Dict[str, any]:
        """Get risk management summary"""
        self.reset_daily_stats()
        
        total_exposure = sum(pos.market_value for pos in portfolio.open_positions)
        exposure_percent = (total_exposure / config.capital) * 100 if config.capital > 0 else 0
        
        return {
            "daily_pnl": self.daily_pnl,
            "daily_trades": self.daily_trades,
            "max_daily_loss_limit": config.max_daily_loss,
            "remaining_daily_loss": config.max_daily_loss + self.daily_pnl,
            "open_positions": len(portfolio.open_positions),
            "max_positions": config.max_positions,
            "total_exposure": total_exposure,
            "exposure_percent": exposure_percent,
            "max_drawdown": self.max_drawdown,
            "peak_value": self.peak_value,
            "available_cash": portfolio.cash
        }
